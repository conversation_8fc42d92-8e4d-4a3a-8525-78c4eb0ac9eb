import {
  integer,
  pgTable,
  varchar,
  text,
  timestamp,
  boolean,
  decimal,
  pgEnum,
  uuid,
  serial
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// 枚举定义
export const linkTypeEnum = pgEnum('link_type', ['profile', 'post', 'comment', 'signature', 'bio', 'other']);
export const linkStatusEnum = pgEnum('link_status', ['pending', 'approved', 'rejected', 'active', 'inactive']);
export const auditStatusEnum = pgEnum('audit_status', ['pending', 'approved', 'rejected']);
export const paymentStatusEnum = pgEnum('payment_status', ['free', 'paid', 'pending_payment']);

// 网站表 - 存储可以发外链的网站信息
export const websites = pgTable('websites', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(), // 网站名称
  domain: varchar('domain', { length: 255 }).notNull().unique(), // 网站域名
  description: text('description'), // 网站描述
  logoUrl: varchar('logo_url', { length: 500 }), // 网站logo
  isActive: boolean('is_active').default(true).notNull(), // 是否启用
  requiresApproval: boolean('requires_approval').default(true).notNull(), // 是否需要审核
  basePrice: decimal('base_price', { precision: 10, scale: 2 }).default('0.00'), // 基础价格
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// 外链类型表 - 定义不同的外链发布方式
export const linkTypes = pgTable('link_types', {
  id: uuid('id').primaryKey().defaultRandom(),
  websiteId: uuid('website_id').references(() => websites.id, { onDelete: 'cascade' }).notNull(),
  type: linkTypeEnum('type').notNull(), // 外链类型
  name: varchar('name', { length: 100 }).notNull(), // 类型名称
  description: text('description'), // 类型描述
  isActive: boolean('is_active').default(true).notNull(),
  requiresApproval: boolean('requires_approval').default(true).notNull(), // 是否需要审核
  price: decimal('price', { precision: 10, scale: 2 }).default('0.00'), // 该类型的价格
  maxLinks: integer('max_links').default(1), // 最大链接数量
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// 外链表 - 存储具体的外链信息
export const backlinks = pgTable('backlinks', {
  id: uuid('id').primaryKey().defaultRandom(),
  websiteId: uuid('website_id').references(() => websites.id, { onDelete: 'cascade' }).notNull(),
  linkTypeId: uuid('link_type_id').references(() => linkTypes.id, { onDelete: 'cascade' }).notNull(),
  targetUrl: varchar('target_url', { length: 500 }).notNull(), // 目标链接
  anchorText: varchar('anchor_text', { length: 255 }), // 锚文本
  publishedUrl: varchar('published_url', { length: 500 }), // 发布后的页面URL
  title: varchar('title', { length: 255 }), // 链接标题
  description: text('description'), // 链接描述
  status: linkStatusEnum('status').default('pending').notNull(),
  paymentStatus: paymentStatusEnum('payment_status').default('free').notNull(),
  price: decimal('price', { precision: 10, scale: 2 }).default('0.00'), // 实际支付价格
  notes: text('notes'), // 备注信息
  publishedAt: timestamp('published_at'), // 发布时间
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// 审核记录表
export const auditLogs = pgTable('audit_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  backlinkId: uuid('backlink_id').references(() => backlinks.id, { onDelete: 'cascade' }).notNull(),
  status: auditStatusEnum('status').notNull(),
  reviewerNotes: text('reviewer_notes'), // 审核员备注
  reviewedAt: timestamp('reviewed_at').defaultNow().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// 定义表关系
export const websitesRelations = relations(websites, ({ many }) => ({
  linkTypes: many(linkTypes),
  backlinks: many(backlinks),
}));

export const linkTypesRelations = relations(linkTypes, ({ one, many }) => ({
  website: one(websites, {
    fields: [linkTypes.websiteId],
    references: [websites.id],
  }),
  backlinks: many(backlinks),
}));

export const backlinksRelations = relations(backlinks, ({ one, many }) => ({
  website: one(websites, {
    fields: [backlinks.websiteId],
    references: [websites.id],
  }),
  linkType: one(linkTypes, {
    fields: [backlinks.linkTypeId],
    references: [linkTypes.id],
  }),
  auditLogs: many(auditLogs),
}));

export const auditLogsRelations = relations(auditLogs, ({ one }) => ({
  backlink: one(backlinks, {
    fields: [auditLogs.backlinkId],
    references: [backlinks.id],
  }),
}));

// TypeScript 类型定义
export type Website = typeof websites.$inferSelect;
export type NewWebsite = typeof websites.$inferInsert;
export type LinkType = typeof linkTypes.$inferSelect;
export type NewLinkType = typeof linkTypes.$inferInsert;
export type Backlink = typeof backlinks.$inferSelect;
export type NewBacklink = typeof backlinks.$inferInsert;
export type AuditLog = typeof auditLogs.$inferSelect;
export type NewAuditLog = typeof auditLogs.$inferInsert;
