# 外链管理系统

一个基于 Next.js 的现代化外链管理系统，用于管理可以发布外链的网站、跟踪外链状态、处理审核流程和管理收费。

## 功能特性

- 🌐 **网站管理**: 管理可以发布外链的网站信息
- 🔗 **外链管理**: 创建、编辑、删除和跟踪外链
- 📝 **多种外链类型**: 支持 profile、post、comment、signature、bio 等多种外链形式
- ✅ **审核系统**: 完整的外链审核流程，支持待审核、已通过、已拒绝状态
- 💰 **收费管理**: 支持免费和付费外链，灵活的价格设置
- 🔍 **搜索过滤**: 强大的搜索和过滤功能
- 📊 **数据统计**: 实时的数据统计和仪表板
- 🎨 **现代化UI**: 使用 Tailwind CSS 构建的响应式界面

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript
- **样式**: Tailwind CSS 4
- **数据库**: PostgreSQL (开发) / Cloudflare D1 (生产)
- **ORM**: Drizzle ORM
- **图标**: Heroicons
- **部署**: Cloudflare Pages (可选)

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd backlinks
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 环境配置

复制环境变量文件并配置：

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，设置数据库连接：

```env
DATABASE_URL=postgresql://username:password@localhost:5432/backlinks
```

### 4. 数据库设置

生成并运行数据库迁移：

```bash
# 生成迁移文件
pnpm drizzle-kit generate

# 运行迁移（需要先创建数据库）
pnpm drizzle-kit migrate
```

### 5. 启动开发服务器

```bash
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 数据库结构

### 主要表结构

- **websites**: 网站信息表
- **link_types**: 外链类型表
- **backlinks**: 外链表
- **audit_logs**: 审核日志表

### 关系说明

- 一个网站可以有多种外链类型
- 一个外链类型可以有多个外链
- 每个外链都有对应的审核记录

## API 接口

### 网站管理

- `GET /api/websites` - 获取网站列表
- `POST /api/websites` - 创建新网站
- `GET /api/websites/[id]` - 获取网站详情
- `PUT /api/websites/[id]` - 更新网站信息
- `DELETE /api/websites/[id]` - 删除网站

### 外链类型管理

- `GET /api/websites/[id]/link-types` - 获取网站的外链类型
- `POST /api/websites/[id]/link-types` - 创建外链类型

### 外链管理

- `GET /api/backlinks` - 获取外链列表
- `POST /api/backlinks` - 创建新外链
- `GET /api/backlinks/[id]` - 获取外链详情
- `PUT /api/backlinks/[id]` - 更新外链信息
- `DELETE /api/backlinks/[id]` - 删除外链

### 审核管理

- `POST /api/backlinks/[id]/audit` - 审核外链
- `GET /api/backlinks/[id]/audit` - 获取审核历史

## 使用说明

### 1. 添加网站

1. 访问"网站管理"页面
2. 点击"添加网站"按钮
3. 填写网站信息：
   - 网站名称（必填）
   - 域名（必填）
   - 描述（可选）
   - Logo URL（可选）
   - 基础价格
   - 是否启用
   - 是否需要审核

### 2. 配置外链类型

1. 进入网站详情页面
2. 添加外链类型：
   - Profile: 个人资料页面链接
   - Post: 文章内容链接
   - Comment: 评论区链接
   - Signature: 签名档链接
   - Bio: 个人简介链接
   - Other: 其他类型

### 3. 创建外链

1. 访问"外链管理"页面
2. 点击"创建外链"按钮
3. 选择目标网站和外链类型
4. 填写外链信息：
   - 目标URL（必填）
   - 锚文本
   - 标题
   - 描述
   - 价格设置

### 4. 审核外链

1. 访问"审核中心"或外链详情页面
2. 查看待审核的外链
3. 选择审核结果：
   - 通过：外链状态变为已通过
   - 拒绝：外链状态变为已拒绝
   - 待定：保持待审核状态
4. 添加审核备注

## 部署

### Cloudflare Pages 部署

1. 构建项目：
```bash
pnpm build
```

2. 使用 Wrangler 部署：
```bash
pnpm wrangler pages deploy
```

### 传统部署

项目也支持部署到 Vercel、Netlify 等平台，只需要配置相应的环境变量。

## 开发

### 项目结构

```
├── app/                    # Next.js App Router
│   ├── api/               # API 路由
│   ├── websites/          # 网站管理页面
│   ├── backlinks/         # 外链管理页面
│   └── globals.css        # 全局样式
├── lib/                   # 工具库
│   ├── db/               # 数据库相关
│   │   └── schema.ts     # 数据库模式
│   └── db.ts             # 数据库连接
├── drizzle/              # 数据库迁移文件
└── drizzle.config.ts     # Drizzle 配置
```

### 开发命令

```bash
# 开发服务器
pnpm dev

# 构建项目
pnpm build

# 启动生产服务器
pnpm start

# 代码检查
pnpm lint

# 生成数据库迁移
pnpm drizzle-kit generate

# 运行数据库迁移
pnpm drizzle-kit migrate

# 查看数据库
pnpm drizzle-kit studio
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

MIT License
