'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface Backlink {
  id: string;
  targetUrl: string;
  anchorText: string | null;
  publishedUrl: string | null;
  title: string | null;
  description: string | null;
  status: 'pending' | 'approved' | 'rejected' | 'active' | 'inactive';
  paymentStatus: 'free' | 'paid' | 'pending_payment';
  price: string;
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
  website: {
    id: string;
    name: string;
    domain: string;
  };
  linkType: {
    id: string;
    type: string;
    name: string;
  };
}

interface BacklinkResponse {
  data: Backlink[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

import { LinkIcon, PlusIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

export default function BacklinksPage() {
  const [backlinks, setBacklinks] = useState<Backlink[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [paymentFilter, setPaymentFilter] = useState('');
  const [page, setPage] = useState(1);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  const fetchBacklinks = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(search && { search }),
        ...(statusFilter && { status: statusFilter }),
        ...(paymentFilter && { paymentStatus: paymentFilter }),
      });

      const response = await fetch(`/api/backlinks?${params}`);
      if (!response.ok) throw new Error('Failed to fetch backlinks');
      
      const data: BacklinkResponse = await response.json();
      setBacklinks(data.data);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching backlinks:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBacklinks();
  }, [page, search, statusFilter, paymentFilter]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    fetchBacklinks();
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', text: '待审核' },
      approved: { color: 'bg-green-100 text-green-800', text: '已通过' },
      rejected: { color: 'bg-red-100 text-red-800', text: '已拒绝' },
      active: { color: 'bg-blue-100 text-blue-800', text: '活跃' },
      inactive: { color: 'bg-gray-100 text-gray-800', text: '非活跃' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const getPaymentBadge = (paymentStatus: string) => {
    const paymentConfig = {
      free: { color: 'bg-green-100 text-green-800', text: '免费' },
      paid: { color: 'bg-blue-100 text-blue-800', text: '已付费' },
      pending_payment: { color: 'bg-yellow-100 text-yellow-800', text: '待付费' },
    };
    
    const config = paymentConfig[paymentStatus as keyof typeof paymentConfig] || paymentConfig.free;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <LinkIcon className="h-8 w-8 text-blue-600" />
                <h1 className="ml-2 text-xl font-bold text-gray-900">外链管理系统</h1>
              </Link>
            </div>
            <nav className="flex space-x-4">
              <Link href="/" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                首页
              </Link>
              <Link href="/websites" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                网站管理
              </Link>
              <Link href="/backlinks" className="text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                外链管理
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">外链管理</h2>
            <p className="text-gray-600">管理所有外链的状态和信息</p>
          </div>
          <Link
            href="/backlinks/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            创建外链
          </Link>
        </div>

        {/* Filters */}
        <div className="mb-6 space-y-4">
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索目标URL、锚文本、标题或网站..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              搜索
            </button>
          </form>

          <div className="flex gap-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">所有状态</option>
              <option value="pending">待审核</option>
              <option value="approved">已通过</option>
              <option value="rejected">已拒绝</option>
              <option value="active">活跃</option>
              <option value="inactive">非活跃</option>
            </select>

            <select
              value={paymentFilter}
              onChange={(e) => setPaymentFilter(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">所有付费状态</option>
              <option value="free">免费</option>
              <option value="paid">已付费</option>
              <option value="pending_payment">待付费</option>
            </select>
          </div>
        </div>

        {/* Backlinks List */}
        {loading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">加载中...</p>
          </div>
        ) : (
          <>
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {backlinks.map((backlink) => (
                  <li key={backlink.id}>
                    <Link href={`/backlinks/${backlink.id}`} className="block hover:bg-gray-50">
                      <div className="px-4 py-4 sm:px-6">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-2">
                              {getStatusBadge(backlink.status)}
                              {getPaymentBadge(backlink.paymentStatus)}
                              <span className="text-xs text-gray-500">
                                {backlink.linkType.name} · {backlink.website.name}
                              </span>
                            </div>
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {backlink.title || backlink.anchorText || '无标题'}
                            </p>
                            <p className="text-sm text-blue-600 truncate">
                              {backlink.targetUrl}
                            </p>
                            {backlink.publishedUrl && (
                              <p className="text-sm text-gray-500 truncate">
                                发布于: {backlink.publishedUrl}
                              </p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium text-gray-900">¥{backlink.price}</p>
                            <p className="text-xs text-gray-500">
                              创建于 {formatDate(backlink.createdAt)}
                            </p>
                            {backlink.publishedAt && (
                              <p className="text-xs text-gray-500">
                                发布于 {formatDate(backlink.publishedAt)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="mt-6 flex justify-center">
                <nav className="flex space-x-2">
                  <button
                    onClick={() => setPage(Math.max(1, page - 1))}
                    disabled={page === 1}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                  >
                    上一页
                  </button>
                  <span className="px-3 py-2 text-sm font-medium text-gray-700">
                    第 {pagination.page} 页，共 {pagination.totalPages} 页
                  </span>
                  <button
                    onClick={() => setPage(Math.min(pagination.totalPages, page + 1))}
                    disabled={page === pagination.totalPages}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                  >
                    下一页
                  </button>
                </nav>
              </div>
            )}

            {backlinks.length === 0 && (
              <div className="text-center py-8">
                <LinkIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">暂无外链</h3>
                <p className="mt-1 text-sm text-gray-500">开始创建您的第一个外链</p>
                <div className="mt-6">
                  <Link
                    href="/backlinks/new"
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    创建外链
                  </Link>
                </div>
              </div>
            )}
          </>
        )}
      </main>
    </div>
  );
}
