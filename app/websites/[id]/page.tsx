'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { GlobeAltIcon, ArrowLeftIcon, PencilIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline';

interface Website {
  id: string;
  name: string;
  domain: string;
  description: string | null;
  logoUrl: string | null;
  isActive: boolean;
  requiresApproval: boolean;
  basePrice: string;
  createdAt: string;
  updatedAt: string;
  stats: {
    linkTypes: number;
    backlinks: number;
  };
}

interface LinkType {
  id: string;
  type: string;
  name: string;
  description: string | null;
  isActive: boolean;
  requiresApproval: boolean;
  price: string;
  maxLinks: number;
  createdAt: string;
}

export default function WebsiteDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [website, setWebsite] = useState<Website | null>(null);
  const [linkTypes, setLinkTypes] = useState<LinkType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchWebsite = async () => {
    try {
      const response = await fetch(`/api/websites/${params.id}`);
      if (!response.ok) throw new Error('Failed to fetch website');
      const data = await response.json();
      setWebsite(data);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const fetchLinkTypes = async () => {
    try {
      const response = await fetch(`/api/websites/${params.id}/link-types`);
      if (!response.ok) throw new Error('Failed to fetch link types');
      const data = await response.json();
      setLinkTypes(data.data);
    } catch (err: any) {
      console.error('Error fetching link types:', err);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchWebsite(), fetchLinkTypes()]);
      setLoading(false);
    };
    loadData();
  }, [params.id]);

  const handleDelete = async () => {
    if (!confirm('确定要删除这个网站吗？此操作不可撤销。')) return;

    try {
      const response = await fetch(`/api/websites/${params.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete website');
      }

      router.push('/websites');
    } catch (err: any) {
      alert(`删除失败: ${err.message}`);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTypeDisplayName = (type: string) => {
    const typeMap: Record<string, string> = {
      profile: '个人资料',
      post: '文章内容',
      comment: '评论区',
      signature: '签名档',
      bio: '个人简介',
      other: '其他',
    };
    return typeMap[type] || type;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error || !website) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error || '网站不存在'}</p>
          <Link
            href="/websites"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            返回网站列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <GlobeAltIcon className="h-8 w-8 text-blue-600" />
                <h1 className="ml-2 text-xl font-bold text-gray-900">外链管理系统</h1>
              </Link>
            </div>
            <nav className="flex space-x-4">
              <Link href="/" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                首页
              </Link>
              <Link href="/websites" className="text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
                网站管理
              </Link>
              <Link href="/backlinks" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                外链管理
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="mb-8">
          <Link
            href="/websites"
            className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            返回网站列表
          </Link>
        </div>

        {/* Website Info */}
        <div className="bg-white shadow rounded-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {website.logoUrl ? (
                  <img
                    src={website.logoUrl}
                    alt={website.name}
                    className="h-12 w-12 rounded-full"
                  />
                ) : (
                  <div className="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                    <GlobeAltIcon className="h-8 w-8 text-gray-600" />
                  </div>
                )}
                <div className="ml-4">
                  <h2 className="text-xl font-bold text-gray-900">{website.name}</h2>
                  <p className="text-sm text-gray-500">{website.domain}</p>
                </div>
                <span className={`ml-4 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  website.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {website.isActive ? '启用' : '禁用'}
                </span>
              </div>
              <div className="flex space-x-2">
                <Link
                  href={`/websites/${website.id}/edit`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <PencilIcon className="h-4 w-4 mr-1" />
                  编辑
                </Link>
                <button
                  onClick={handleDelete}
                  className="inline-flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                >
                  <TrashIcon className="h-4 w-4 mr-1" />
                  删除
                </button>
              </div>
            </div>
          </div>

          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <dt className="text-sm font-medium text-gray-500">描述</dt>
                <dd className="mt-1 text-sm text-gray-900">{website.description || '无描述'}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">基础价格</dt>
                <dd className="mt-1 text-sm text-gray-900">¥{website.basePrice}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">需要审核</dt>
                <dd className="mt-1 text-sm text-gray-900">{website.requiresApproval ? '是' : '否'}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">创建时间</dt>
                <dd className="mt-1 text-sm text-gray-900">{formatDate(website.createdAt)}</dd>
              </div>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <PlusIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">外链类型</p>
                <p className="text-2xl font-semibold text-gray-900">{website.stats.linkTypes}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <GlobeAltIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">外链总数</p>
                <p className="text-2xl font-semibold text-gray-900">{website.stats.backlinks}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Link Types */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">外链类型</h3>
              <button className="inline-flex items-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                <PlusIcon className="h-4 w-4 mr-1" />
                添加类型
              </button>
            </div>
          </div>

          {linkTypes.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {linkTypes.map((linkType) => (
                <div key={linkType.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center">
                        <h4 className="text-sm font-medium text-gray-900">{linkType.name}</h4>
                        <span className="ml-2 text-xs text-gray-500">
                          ({getTypeDisplayName(linkType.type)})
                        </span>
                        <span className={`ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                          linkType.isActive 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {linkType.isActive ? '启用' : '禁用'}
                        </span>
                      </div>
                      {linkType.description && (
                        <p className="mt-1 text-sm text-gray-500">{linkType.description}</p>
                      )}
                      <div className="mt-1 text-xs text-gray-400">
                        价格: ¥{linkType.price} | 最大链接数: {linkType.maxLinks} | 
                        需要审核: {linkType.requiresApproval ? '是' : '否'}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">¥{linkType.price}</p>
                      <p className="text-xs text-gray-500">
                        创建于 {formatDate(linkType.createdAt)}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="px-6 py-8 text-center">
              <PlusIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无外链类型</h3>
              <p className="mt-1 text-sm text-gray-500">为这个网站添加第一个外链类型</p>
              <div className="mt-6">
                <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                  <PlusIcon className="h-4 w-4 mr-2" />
                  添加外链类型
                </button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
