import { NextRequest, NextResponse } from 'next/server';
import db from '@/lib/db';
import { websites, type NewWebsite } from '@/lib/db/schema';
import { eq, desc, ilike, or } from 'drizzle-orm';

// GET /api/websites - 获取网站列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    let query = db.select().from(websites);

    // 搜索功能
    if (search) {
      query = query.where(
        or(
          ilike(websites.name, `%${search}%`),
          ilike(websites.domain, `%${search}%`),
          ilike(websites.description, `%${search}%`)
        )
      );
    }

    const result = await query
      .orderBy(desc(websites.createdAt))
      .limit(limit)
      .offset(offset);

    // 获取总数
    let totalQuery = db.select({ count: count() }).from(websites);
    if (search) {
      totalQuery = totalQuery.where(
        or(
          ilike(websites.name, `%${search}%`),
          ilike(websites.domain, `%${search}%`),
          ilike(websites.description, `%${search}%`)
        )
      );
    }

    const [{ count: total }] = await totalQuery;

    return NextResponse.json({
      data: result,
      pagination: {
        page,
        limit,
        total: Number(total),
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching websites:', error);
    return NextResponse.json(
      { error: 'Failed to fetch websites' },
      { status: 500 }
    );
  }
}

// POST /api/websites - 创建新网站
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必填字段
    if (!body.name || !body.domain) {
      return NextResponse.json(
        { error: 'Name and domain are required' },
        { status: 400 }
      );
    }

    // 验证域名格式
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/;
    if (!domainRegex.test(body.domain)) {
      return NextResponse.json(
        { error: 'Invalid domain format' },
        { status: 400 }
      );
    }

    const newWebsite: NewWebsite = {
      name: body.name,
      domain: body.domain,
      description: body.description || null,
      logoUrl: body.logoUrl || null,
      isActive: body.isActive ?? true,
      requiresApproval: body.requiresApproval ?? true,
      basePrice: body.basePrice || '0.00',
    };

    const [result] = await db.insert(websites).values(newWebsite).returning();

    return NextResponse.json(result, { status: 201 });
  } catch (error: any) {
    console.error('Error creating website:', error);
    
    // 处理唯一约束错误
    if (error.code === '23505' && error.constraint === 'websites_domain_unique') {
      return NextResponse.json(
        { error: 'Domain already exists' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create website' },
      { status: 500 }
    );
  }
}
