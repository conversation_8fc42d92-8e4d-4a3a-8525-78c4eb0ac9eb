import { NextRequest, NextResponse } from 'next/server';
import db from '@/lib/db';
import { websites, linkTypes, backlinks } from '@/lib/db/schema';
import { eq, sql } from 'drizzle-orm';

// GET /api/websites/[id] - 获取单个网站详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // 获取网站基本信息
    const [website] = await db
      .select()
      .from(websites)
      .where(eq(websites.id, id));

    if (!website) {
      return NextResponse.json(
        { error: 'Website not found' },
        { status: 404 }
      );
    }

    // 获取关联的链接类型数量
    const [linkTypeCount] = await db
      .select({ count: sql`count(*)`.mapWith(Number) })
      .from(linkTypes)
      .where(eq(linkTypes.websiteId, id));

    // 获取关联的外链数量
    const [backlinkCount] = await db
      .select({ count: sql`count(*)`.mapWith(Number) })
      .from(backlinks)
      .where(eq(backlinks.websiteId, id));

    const result = {
      ...website,
      stats: {
        linkTypes: linkTypeCount.count,
        backlinks: backlinkCount.count,
      },
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching website:', error);
    return NextResponse.json(
      { error: 'Failed to fetch website' },
      { status: 500 }
    );
  }
}

// PUT /api/websites/[id] - 更新网站信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // 验证网站是否存在
    const [existingWebsite] = await db
      .select()
      .from(websites)
      .where(eq(websites.id, id));

    if (!existingWebsite) {
      return NextResponse.json(
        { error: 'Website not found' },
        { status: 404 }
      );
    }

    // 验证域名格式（如果提供了域名）
    if (body.domain) {
      const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/;
      if (!domainRegex.test(body.domain)) {
        return NextResponse.json(
          { error: 'Invalid domain format' },
          { status: 400 }
        );
      }
    }

    const updateData: Partial<typeof websites.$inferInsert> = {
      updatedAt: new Date(),
    };

    // 只更新提供的字段
    if (body.name !== undefined) updateData.name = body.name;
    if (body.domain !== undefined) updateData.domain = body.domain;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.logoUrl !== undefined) updateData.logoUrl = body.logoUrl;
    if (body.isActive !== undefined) updateData.isActive = body.isActive;
    if (body.requiresApproval !== undefined) updateData.requiresApproval = body.requiresApproval;
    if (body.basePrice !== undefined) updateData.basePrice = body.basePrice;

    const [result] = await db
      .update(websites)
      .set(updateData)
      .where(eq(websites.id, id))
      .returning();

    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error updating website:', error);
    
    // 处理唯一约束错误
    if (error.code === '23505' && error.constraint === 'websites_domain_unique') {
      return NextResponse.json(
        { error: 'Domain already exists' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update website' },
      { status: 500 }
    );
  }
}

// DELETE /api/websites/[id] - 删除网站
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // 验证网站是否存在
    const [existingWebsite] = await db
      .select()
      .from(websites)
      .where(eq(websites.id, id));

    if (!existingWebsite) {
      return NextResponse.json(
        { error: 'Website not found' },
        { status: 404 }
      );
    }

    // 检查是否有关联的外链
    const [backlinkCount] = await db
      .select({ count: sql`count(*)`.mapWith(Number) })
      .from(backlinks)
      .where(eq(backlinks.websiteId, id));

    if (backlinkCount.count > 0) {
      return NextResponse.json(
        { error: 'Cannot delete website with existing backlinks' },
        { status: 400 }
      );
    }

    await db.delete(websites).where(eq(websites.id, id));

    return NextResponse.json({ message: 'Website deleted successfully' });
  } catch (error) {
    console.error('Error deleting website:', error);
    return NextResponse.json(
      { error: 'Failed to delete website' },
      { status: 500 }
    );
  }
}
