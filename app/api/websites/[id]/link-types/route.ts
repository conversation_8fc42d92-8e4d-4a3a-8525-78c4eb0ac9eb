import { NextRequest, NextResponse } from 'next/server';
import db from '@/lib/db';
import { linkTypes, websites, type NewLinkType } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';

// GET /api/websites/[id]/link-types - 获取网站的链接类型列表
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // 验证网站是否存在
    const [website] = await db
      .select()
      .from(websites)
      .where(eq(websites.id, id));

    if (!website) {
      return NextResponse.json(
        { error: 'Website not found' },
        { status: 404 }
      );
    }

    const result = await db
      .select()
      .from(linkTypes)
      .where(eq(linkTypes.websiteId, id))
      .orderBy(desc(linkTypes.createdAt));

    return NextResponse.json({ data: result });
  } catch (error) {
    console.error('Error fetching link types:', error);
    return NextResponse.json(
      { error: 'Failed to fetch link types' },
      { status: 500 }
    );
  }
}

// POST /api/websites/[id]/link-types - 为网站创建新的链接类型
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();

    // 验证网站是否存在
    const [website] = await db
      .select()
      .from(websites)
      .where(eq(websites.id, id));

    if (!website) {
      return NextResponse.json(
        { error: 'Website not found' },
        { status: 404 }
      );
    }

    // 验证必填字段
    if (!body.type || !body.name) {
      return NextResponse.json(
        { error: 'Type and name are required' },
        { status: 400 }
      );
    }

    // 验证类型枚举值
    const validTypes = ['profile', 'post', 'comment', 'signature', 'bio', 'other'];
    if (!validTypes.includes(body.type)) {
      return NextResponse.json(
        { error: 'Invalid link type' },
        { status: 400 }
      );
    }

    const newLinkType: NewLinkType = {
      websiteId: id,
      type: body.type,
      name: body.name,
      description: body.description || null,
      isActive: body.isActive ?? true,
      requiresApproval: body.requiresApproval ?? true,
      price: body.price || '0.00',
      maxLinks: body.maxLinks || 1,
    };

    const [result] = await db.insert(linkTypes).values(newLinkType).returning();

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error creating link type:', error);
    return NextResponse.json(
      { error: 'Failed to create link type' },
      { status: 500 }
    );
  }
}
