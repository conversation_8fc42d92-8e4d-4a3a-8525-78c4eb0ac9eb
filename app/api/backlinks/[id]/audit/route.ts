import { NextRequest, NextResponse } from 'next/server';
import db from '@/lib/db';
import { backlinks, auditLogs, type NewAuditLog } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// POST /api/backlinks/[id]/audit - 审核外链
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // 验证必填字段
    if (!body.status) {
      return NextResponse.json(
        { error: 'Audit status is required' },
        { status: 400 }
      );
    }

    // 验证状态值
    const validStatuses = ['approved', 'rejected', 'pending'];
    if (!validStatuses.includes(body.status)) {
      return NextResponse.json(
        { error: 'Invalid audit status' },
        { status: 400 }
      );
    }

    // 验证外链是否存在
    const [existingBacklink] = await db
      .select()
      .from(backlinks)
      .where(eq(backlinks.id, id));

    if (!existingBacklink) {
      return NextResponse.json(
        { error: 'Backlink not found' },
        { status: 404 }
      );
    }

    // 开始事务
    const result = await db.transaction(async (tx) => {
      // 更新外链状态
      const newStatus = body.status === 'approved' ? 'approved' : 
                       body.status === 'rejected' ? 'rejected' : 'pending';

      const [updatedBacklink] = await tx
        .update(backlinks)
        .set({
          status: newStatus,
          updatedAt: new Date(),
          publishedAt: body.status === 'approved' ? new Date() : existingBacklink.publishedAt,
        })
        .where(eq(backlinks.id, id))
        .returning();

      // 创建审核日志
      const auditLog: NewAuditLog = {
        backlinkId: id,
        status: body.status,
        reviewerNotes: body.reviewerNotes || null,
      };

      const [createdAuditLog] = await tx
        .insert(auditLogs)
        .values(auditLog)
        .returning();

      return {
        backlink: updatedBacklink,
        auditLog: createdAuditLog,
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error auditing backlink:', error);
    return NextResponse.json(
      { error: 'Failed to audit backlink' },
      { status: 500 }
    );
  }
}

// GET /api/backlinks/[id]/audit - 获取外链审核历史
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // 验证外链是否存在
    const [existingBacklink] = await db
      .select()
      .from(backlinks)
      .where(eq(backlinks.id, id));

    if (!existingBacklink) {
      return NextResponse.json(
        { error: 'Backlink not found' },
        { status: 404 }
      );
    }

    // 获取审核历史
    const auditHistory = await db
      .select()
      .from(auditLogs)
      .where(eq(auditLogs.backlinkId, id))
      .orderBy(auditLogs.reviewedAt);

    return NextResponse.json({ data: auditHistory });
  } catch (error) {
    console.error('Error fetching audit history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch audit history' },
      { status: 500 }
    );
  }
}
