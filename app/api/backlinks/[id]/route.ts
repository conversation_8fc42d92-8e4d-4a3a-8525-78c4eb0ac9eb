import { NextRequest, NextResponse } from 'next/server';
import db from '@/lib/db';
import { backlinks, websites, linkTypes, auditLogs, type NewAuditLog } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// GET /api/backlinks/[id] - 获取单个外链详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const [result] = await db
      .select({
        id: backlinks.id,
        targetUrl: backlinks.targetUrl,
        anchorText: backlinks.anchorText,
        publishedUrl: backlinks.publishedUrl,
        title: backlinks.title,
        description: backlinks.description,
        status: backlinks.status,
        paymentStatus: backlinks.paymentStatus,
        price: backlinks.price,
        notes: backlinks.notes,
        publishedAt: backlinks.publishedAt,
        createdAt: backlinks.createdAt,
        updatedAt: backlinks.updatedAt,
        website: {
          id: websites.id,
          name: websites.name,
          domain: websites.domain,
          logoUrl: websites.logoUrl,
        },
        linkType: {
          id: linkTypes.id,
          type: linkTypes.type,
          name: linkTypes.name,
          description: linkTypes.description,
        },
      })
      .from(backlinks)
      .leftJoin(websites, eq(backlinks.websiteId, websites.id))
      .leftJoin(linkTypes, eq(backlinks.linkTypeId, linkTypes.id))
      .where(eq(backlinks.id, id));

    if (!result) {
      return NextResponse.json(
        { error: 'Backlink not found' },
        { status: 404 }
      );
    }

    // 获取审核历史
    const auditHistory = await db
      .select()
      .from(auditLogs)
      .where(eq(auditLogs.backlinkId, id))
      .orderBy(auditLogs.reviewedAt);

    return NextResponse.json({
      ...result,
      auditHistory,
    });
  } catch (error) {
    console.error('Error fetching backlink:', error);
    return NextResponse.json(
      { error: 'Failed to fetch backlink' },
      { status: 500 }
    );
  }
}

// PUT /api/backlinks/[id] - 更新外链信息
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();

    // 验证外链是否存在
    const [existingBacklink] = await db
      .select()
      .from(backlinks)
      .where(eq(backlinks.id, id));

    if (!existingBacklink) {
      return NextResponse.json(
        { error: 'Backlink not found' },
        { status: 404 }
      );
    }

    // 验证URL格式（如果提供了URL）
    if (body.targetUrl) {
      try {
        new URL(body.targetUrl);
      } catch {
        return NextResponse.json(
          { error: 'Invalid target URL format' },
          { status: 400 }
        );
      }
    }

    if (body.publishedUrl) {
      try {
        new URL(body.publishedUrl);
      } catch {
        return NextResponse.json(
          { error: 'Invalid published URL format' },
          { status: 400 }
        );
      }
    }

    const updateData: Partial<typeof backlinks.$inferInsert> = {
      updatedAt: new Date(),
    };

    // 只更新提供的字段
    if (body.targetUrl !== undefined) updateData.targetUrl = body.targetUrl;
    if (body.anchorText !== undefined) updateData.anchorText = body.anchorText;
    if (body.publishedUrl !== undefined) updateData.publishedUrl = body.publishedUrl;
    if (body.title !== undefined) updateData.title = body.title;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.status !== undefined) updateData.status = body.status;
    if (body.paymentStatus !== undefined) updateData.paymentStatus = body.paymentStatus;
    if (body.price !== undefined) updateData.price = body.price;
    if (body.notes !== undefined) updateData.notes = body.notes;
    if (body.publishedAt !== undefined) updateData.publishedAt = body.publishedAt ? new Date(body.publishedAt) : null;

    const [result] = await db
      .update(backlinks)
      .set(updateData)
      .where(eq(backlinks.id, id))
      .returning();

    // 如果状态发生变化，记录审核日志
    if (body.status && body.status !== existingBacklink.status) {
      const auditLog: NewAuditLog = {
        backlinkId: id,
        status: body.status === 'approved' ? 'approved' : body.status === 'rejected' ? 'rejected' : 'pending',
        reviewerNotes: body.reviewerNotes || null,
      };

      await db.insert(auditLogs).values(auditLog);
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating backlink:', error);
    return NextResponse.json(
      { error: 'Failed to update backlink' },
      { status: 500 }
    );
  }
}

// DELETE /api/backlinks/[id] - 删除外链
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // 验证外链是否存在
    const [existingBacklink] = await db
      .select()
      .from(backlinks)
      .where(eq(backlinks.id, id));

    if (!existingBacklink) {
      return NextResponse.json(
        { error: 'Backlink not found' },
        { status: 404 }
      );
    }

    await db.delete(backlinks).where(eq(backlinks.id, id));

    return NextResponse.json({ message: 'Backlink deleted successfully' });
  } catch (error) {
    console.error('Error deleting backlink:', error);
    return NextResponse.json(
      { error: 'Failed to delete backlink' },
      { status: 500 }
    );
  }
}
