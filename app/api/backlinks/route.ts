import { NextRequest, NextResponse } from 'next/server';
import db from '@/lib/db';
import { backlinks, websites, linkTypes, type NewBacklink } from '@/lib/db/schema';
import { eq, desc, ilike, or, and, sql } from 'drizzle-orm';

// GET /api/backlinks - 获取外链列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const websiteId = searchParams.get('websiteId');
    const paymentStatus = searchParams.get('paymentStatus');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // 构建查询
    let query = db
      .select({
        id: backlinks.id,
        targetUrl: backlinks.targetUrl,
        anchorText: backlinks.anchorText,
        publishedUrl: backlinks.publishedUrl,
        title: backlinks.title,
        description: backlinks.description,
        status: backlinks.status,
        paymentStatus: backlinks.paymentStatus,
        price: backlinks.price,
        publishedAt: backlinks.publishedAt,
        createdAt: backlinks.createdAt,
        updatedAt: backlinks.updatedAt,
        website: {
          id: websites.id,
          name: websites.name,
          domain: websites.domain,
        },
        linkType: {
          id: linkTypes.id,
          type: linkTypes.type,
          name: linkTypes.name,
        },
      })
      .from(backlinks)
      .leftJoin(websites, eq(backlinks.websiteId, websites.id))
      .leftJoin(linkTypes, eq(backlinks.linkTypeId, linkTypes.id));

    // 应用过滤条件
    const conditions = [];

    if (search) {
      conditions.push(
        or(
          ilike(backlinks.targetUrl, `%${search}%`),
          ilike(backlinks.anchorText, `%${search}%`),
          ilike(backlinks.title, `%${search}%`),
          ilike(websites.name, `%${search}%`),
          ilike(websites.domain, `%${search}%`)
        )
      );
    }

    if (status) {
      conditions.push(eq(backlinks.status, status as any));
    }

    if (websiteId) {
      conditions.push(eq(backlinks.websiteId, websiteId));
    }

    if (paymentStatus) {
      conditions.push(eq(backlinks.paymentStatus, paymentStatus as any));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    const result = await query
      .orderBy(desc(backlinks.createdAt))
      .limit(limit)
      .offset(offset);

    // 获取总数
    let countQuery = db
      .select({ count: sql`count(*)`.mapWith(Number) })
      .from(backlinks)
      .leftJoin(websites, eq(backlinks.websiteId, websites.id));

    if (conditions.length > 0) {
      countQuery = countQuery.where(and(...conditions));
    }

    const [{ count: total }] = await countQuery;

    return NextResponse.json({
      data: result,
      pagination: {
        page,
        limit,
        total: Number(total),
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching backlinks:', error);
    return NextResponse.json(
      { error: 'Failed to fetch backlinks' },
      { status: 500 }
    );
  }
}

// POST /api/backlinks - 创建新外链
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证必填字段
    if (!body.websiteId || !body.linkTypeId || !body.targetUrl) {
      return NextResponse.json(
        { error: 'Website ID, link type ID, and target URL are required' },
        { status: 400 }
      );
    }

    // 验证URL格式
    try {
      new URL(body.targetUrl);
    } catch {
      return NextResponse.json(
        { error: 'Invalid target URL format' },
        { status: 400 }
      );
    }

    // 验证网站和链接类型是否存在
    const [website] = await db
      .select()
      .from(websites)
      .where(eq(websites.id, body.websiteId));

    if (!website) {
      return NextResponse.json(
        { error: 'Website not found' },
        { status: 404 }
      );
    }

    const [linkType] = await db
      .select()
      .from(linkTypes)
      .where(eq(linkTypes.id, body.linkTypeId));

    if (!linkType) {
      return NextResponse.json(
        { error: 'Link type not found' },
        { status: 404 }
      );
    }

    // 验证链接类型是否属于指定网站
    if (linkType.websiteId !== body.websiteId) {
      return NextResponse.json(
        { error: 'Link type does not belong to the specified website' },
        { status: 400 }
      );
    }

    const newBacklink: NewBacklink = {
      websiteId: body.websiteId,
      linkTypeId: body.linkTypeId,
      targetUrl: body.targetUrl,
      anchorText: body.anchorText || null,
      title: body.title || null,
      description: body.description || null,
      status: website.requiresApproval || linkType.requiresApproval ? 'pending' : 'approved',
      paymentStatus: body.paymentStatus || 'free',
      price: body.price || linkType.price || '0.00',
      notes: body.notes || null,
    };

    const [result] = await db.insert(backlinks).values(newBacklink).returning();

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error creating backlink:', error);
    return NextResponse.json(
      { error: 'Failed to create backlink' },
      { status: 500 }
    );
  }
}
