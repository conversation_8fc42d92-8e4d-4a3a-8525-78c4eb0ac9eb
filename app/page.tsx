import Link from "next/link";
import db from "@/lib/db";
import { websites, backlinks } from "@/lib/db/schema";
import { count, eq } from "drizzle-orm";
import {
  LinkIcon,
  GlobeAltIcon,
  ClockIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline";

export default async function Home() {
  // 获取统计数据
  const [websiteCount] = await db.select({ count: count() }).from(websites);
  const [backlinkCount] = await db.select({ count: count() }).from(backlinks);
  const [pendingCount] = await db.select({ count: count() }).from(backlinks).where(eq(backlinks.status, 'pending'));
  const [approvedCount] = await db.select({ count: count() }).from(backlinks).where(eq(backlinks.status, 'approved'));

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <LinkIcon className="h-8 w-8 text-blue-600" />
              <h1 className="ml-2 text-xl font-bold text-gray-900">外链管理系统</h1>
            </div>
            <nav className="flex space-x-4">
              <Link href="/websites" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                网站管理
              </Link>
              <Link href="/backlinks" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                外链管理
              </Link>
              <Link href="/audit" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                审核中心
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">欢迎使用外链管理系统</h2>
          <p className="text-gray-600">管理您的外链发布网站，跟踪外链状态，处理审核流程</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <GlobeAltIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">网站总数</p>
                <p className="text-2xl font-semibold text-gray-900">{websiteCount.count}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <LinkIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">外链总数</p>
                <p className="text-2xl font-semibold text-gray-900">{backlinkCount.count}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">待审核</p>
                <p className="text-2xl font-semibold text-gray-900">{pendingCount.count}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">已通过</p>
                <p className="text-2xl font-semibold text-gray-900">{approvedCount.count}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Link href="/websites/new" className="block">
            <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 border-l-4 border-blue-500">
              <div className="flex items-center">
                <GlobeAltIcon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">添加新网站</h3>
                  <p className="text-sm text-gray-500">添加可以发布外链的网站</p>
                </div>
              </div>
            </div>
          </Link>

          <Link href="/backlinks/new" className="block">
            <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 border-l-4 border-green-500">
              <div className="flex items-center">
                <LinkIcon className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">创建外链</h3>
                  <p className="text-sm text-gray-500">为网站创建新的外链</p>
                </div>
              </div>
            </div>
          </Link>

          <Link href="/audit" className="block">
            <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 border-l-4 border-yellow-500">
              <div className="flex items-center">
                <ClockIcon className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">审核中心</h3>
                  <p className="text-sm text-gray-500">处理待审核的外链</p>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </main>
    </div>
  );
}
